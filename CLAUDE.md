# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **Qt migration project** for KiCad's PCBNew editor - migrating core C++ classes from wxWidgets/KiCad frameworks to Qt frameworks. The goal is to create Qt-based equivalents of KiCad's PCB data model classes while maintaining architectural compatibility.

**Critical Context**: This is NOT a complete KiCad build - it's an isolated Qt migration proof-of-concept focusing on PCB data model classes only. Many dependencies are temporarily mocked in `qt_temporary_implementations.h`.

## 🎯 当前编译状态 (2025-08-06)
每次修改错误都要更新当前的状态到该文件CLAUDE.md 

### ✅ 已解决的关键问题
- **编译器配置**: 成功切换到MSVC编译器，配置Qt MSVC版本
- **继承层次错误**: 修复大量override关键字和虚函数签名不匹配问题
- **EDA_BRD_FIELD_DATA修复**: 
  - setType方法不存在 → 在构造函数中传递正确类型
  - KICAD_T到QtKicadType类型转换 → 统一使用QtKicadType
  - serialize/deserialize参数错误 → 修正方法签名和调用方式
  - LAYER_ID枚举转换 → 使用QtPcbLayerId::FSilkS格式
  layer id统一使用QtPcbLayerId， 放在 qt_temporary_implementations
  元素类型枚举统一使用 QtKicadType ，放在 qt_temporary_implementations
  层集合统一使用QtLayerSet，放在 qt_temporary_implementations
   QtLset 和 QtLayerSet 是同一个概念，要整合到 QtLayerSet
- **虚函数声明**: 移除不正确的override关键字，修正返回类型



### ✅ 新解决的问题 (2025-08-06 最新)
- **EDA_BRD_TEXT_DATA全面修复**:
  - 构造函数参数类型从QtKicadT改为QtKicadType
  - override关键字错误修复，调整虚函数签名匹配基类
  - getMsgPanelInfo方法签名修正为匹配EDA_OBJECT_DATA基类
  - getFontMetrics返回类型从QtFontMetrics&改为QFontMetrics
  - 所有依赖不可用基类方法的代码都注释为占位实现
  - 添加了全套缺失的枚举：QtKicadType、QtFlipDirection、QtFlashing、QtErrorLoc、QtBitmaps
  - 实现了isBackLayer、rotatePoint、mirrorValue等工具函数

- **EDA_ZONE_DATA全面修复**:
  - 修复成员变量名不匹配问题：m_minThickness → m_zoneMinThickness, m_zoneConnection → m_padConnection
  - 修复容器类型：m_filledPolysList从指针改为直接容器，m_outline → m_poly
  - 添加缺失的枚举到qt_temporary_implementations.h：QtZoneConnection, QtZoneBorderStyle
  - 修复所有方法签名匹配头文件声明
  - 修复clone()方法返回类型从EDA_BOARD_OBJECT_DATA*改为EDA_OBJECT_DATA*
  - 添加QtShapePolySet的collision方法声明和实现
  - 修复所有容器访问方式（去掉->改为.）
  - 添加所有缺失的方法：serialize, deserialize, isConnected等
  - 修复getMsgPanelInfo添加const限定符
  - 统一使用标准库指针而非Qt指针

- **QtLayerSet类型统一**:
  - 移除重复定义：从eda_board_object_data.h中移除QtLayerSet类定义
  - 移除QtLset类：统一使用QtLayerSet，在qt_temporary_implementations.h中定义
  - 移除重复的静态方法：从eda_board_object_data.cpp中移除QtLayerSet静态方法实现
  - 修复QtZoneConnection重复定义：从qt_temporary_implementations.h中移除，使用eda_padstack_data.h中的定义
  - 添加构造函数重载：支持QVector<QtPcbLayerId>和QList<QtPcbLayerId>参数
  - 添加缺失方法：any(), empty()等工具方法

- **QtLayerSet复制构造函数和QtLayerIdUtils完整性修复** (2025-08-07):
  - 为QtLayerSet添加显式复制构造函数和赋值操作符，解决"没有适当的复制构造函数"错误
  - 完善QtLayerIdUtils类，添加缺失方法：isCopperLayer(), getLayerOrder(), getLayerFromOrder()
  - 修复E0070不完整类型错误，确保所有引用的方法都有完整实现
  - 解决C2535重复构造函数定义错误：
    * 移除eda_board_connected_object.cpp中重复的qt_temporary_implementations.h包含
    * 为QtLayerSet类添加独立的包含保护（QTLAYERSET_DEFINED）防止多重定义
    * 修复由于复杂头文件依赖链导致的类重复定义问题
  - **C2535/C2084构造函数重复定义修复**：
    * 将QtLayerSet构造函数从声明+外部定义改为内联定义在类内部
    * 解决"函数已有主体"和"已经定义或声明成员函数"错误
    * 确保头文件中的内联函数不会在多个编译单元中产生重复定义
    * **最终解决方案**：移除有问题的列表构造函数，改为静态工厂方法
      - 移除 `QtLayerSet(const QList<QtPcbLayerId>&)` 和 `QtLayerSet(const QVector<QtPcbLayerId>&)` 构造函数
      - 添加 `fromList()` 和 `fromVector()` 静态工厂方法替代
      - 保留单层构造函数 `QtLayerSet(QtPcbLayerId layer)` 并标记为 explicit

- **EDA_ZONE_DATA编译错误修复** (2025-08-07):
  - **QtZoneConnection枚举值修复**：添加缺失的枚举值 INHERIT, NONE, THERMAL, FULL, THT_THERMAL
  - **类型不匹配修复**：m_borderStyle 类型从 QtZoneBorderStyle 改为 QtZoneBorderDisplayStyle
  - **方法签名修复**：getMsgPanelInfo 参数从 QtEdaDrawFrame* 改为 EDA_BOARD_DATA*
  - **未声明方法清理**：移除头文件中未声明的 getArea(), isEmpty(), mirrorPolygon(), flipLayer() 方法
  - **flip方法修复**：使用 QtLayerIdUtils::flipLayer() 和 poly->mirror() 替代不存在的函数
  - **QtLayerSet功能增强**：添加 clear() 方法用于清空所有层
  - **方法参数类型修复**：flip方法参数从QtFlipDirection改为int以匹配头文件声明
  - **赋值操作符修复**：移除对已删除的基类赋值操作符的调用
  - **成员访问修复**：在getMsgPanelInfo中使用this->明确指定成员变量访问
  - **辅助函数添加**：添加fillModeToString()和connectionToString()方法用于枚举值转字符串
  - **C2686静态/非静态重载冲突修复**：移除重复的非静态方法声明，保留静态版本
  - **C2039 mirror方法缺失修复**：为QtShapePolySet添加mirror()方法支持镜像变换
  - **C2084重复函数定义修复**：移除重复的fillModeToString和connectionToString函数定义
  - **C2196重复case值修复**：由于INHERITED和INHERIT具有相同值(-1)，移除重复的case标签

- **EDA_CONNECTIVITY_MANAGER编译错误修复** (2025-08-07):
  - **类重复定义修复**：移除eda_connectivity_manager.cpp中重复的类定义（QtBoardCommit, QtProgressReporter等），改为使用qt_temporary_implementations.h中的定义
  - **未定义成员变量修复**：所有成员变量错误都通过注释掉相关代码解决，因为这些是占位实现
  - **构造函数初始化列表修复**：修正构造函数语法错误，确保成员初始化正确
  - **缺失方法修复**：注释掉所有调用不存在方法的代码，如ensureInitialized(), clearRatsnest()等
  - **翻译函数修复**：移除tr()调用，直接使用字符串字面量
  - **方法签名修复**：确保所有方法声明与实现匹配
  - **Qt对象转换修复**：使用std::shared_ptr替代Qt智能指针
  - **临时实现策略**：对于复杂的连接性算法，暂时使用占位实现以确保编译通过
  - **前向声明清理**：正确处理类的前向声明，避免重复定义错误

### 🚨 新增记忆点
- **翻译策略**: 所有翻译相关的地方，统一使用纯文本，不用翻译
- 所有没有实现的类，前向声明不满足需求并且要实际用到的话，必须在 c:\Users\<USER>\My_Working_Area\MyProject\kicad\kicad-9.0\qt_pcbnew\qt_temporary_implementations.h中进行临时最小化实现，不要再当前文件临时实现
- 关于虚函数错误，一定要跟踪完所有的父类，比如父类，父类的父类，不要仅仅看父类
- 关于打印日志的错误，直接注释掉
- 确保枚举的使用全局唯一，不不清楚枚举放到哪里时，就放到 c:\Users\<USER>\My_Working_Area\MyProject\kicad\kicad-9.0\qt_pcbnew\qt_temporary_implementations.h
- 我现在会逐渐从 'c:/Users/<USER>/My_Working_Area/MyProject/kicad/kicad-9.0/qt_pcbnew/CMakeLists.txt'中把文件把编译文件的前面的注释去掉，一个个编译解决错误
- 你不要自己编译，我会手动编译
- 对于指针，我们只用标准库的，不用Qt的
- 不要使用Qt的指针，使用标准库的
- 永远不要使用Qt的指针，而是标准库的指针
- 由于代码是从kciad迁移过来，如果遇到问题不知道如何解决你必须去参考 C:\Users\<USER>\My_Working_Area\MyProject\kicad\kicad-9.0 目录中的原始实现，一档不要自己自由发挥进行实现
- 那些纯粹是为了图形渲染的方法出现错误，直接删掉就行，不需要，
- 所有的智能指针都使用标准库的 std::shared_ptr 而不是Qt特有的 QSharedPoint

## Architecture

[... rest of the existing content remains unchanged ...]